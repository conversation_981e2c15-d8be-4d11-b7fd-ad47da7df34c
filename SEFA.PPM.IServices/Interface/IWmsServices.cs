using System;
using System.Threading.Tasks;
using SEFA.Base.Model;
using SEFA.PPM.IServices.Models;

namespace SEFA.PPM.IServices;

public interface IWmsServices
{
    /// <summary>
    /// 线边仓叫料接口
    /// </summary>
    /// <param name="request">叫料请求参数</param>
    /// <returns>叫料结果</returns>
    Task<MessageModel<String>> CallMaterialAsync(CallMaterialRequest request);

    /// <summary>
    /// 物料到达通知接口
    /// </summary>
    /// <param name="request">物料到达请求参数</param>
    /// <returns>通知结果</returns>
    Task<MessageModel<bool>> SendDistributionMaterialAsync(DistributionMaterialRequest request);

    /// <summary>
    /// 托盘解绑接口
    /// </summary>
    /// <param name="request">托盘解绑请求参数</param>
    /// <returns>解绑结果</returns>
    Task<MessageModel<PalletReleaseResponse>> PalletReleaseAsync(PalletReleaseRequest request);
}
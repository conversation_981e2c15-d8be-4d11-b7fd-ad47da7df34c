using System;
using System.Collections.Generic;

namespace SEFA.PPM.IServices.Models;

public class CallMaterialRequest
{
    public string RequestSheetNo { get; set; }
    public DateTime RequestTime { get; set; }
    public string LineCode { get; set; }
    public string PositionCode { get; set; }
    public int OperationType { get; set; }
    public string RequestType { get; set; }
    public List<RequestDetail> RequestDetailsList { get; set; }
}

public class RequestDetail
{
    public int DetailsNumber { get; set; }
    public string Plant { get; set; }
    public string MaterialCode { get; set; }
    public string MaterialName { get; set; }
    public string MaterialVersionCode { get; set; }
    public double RequestQty { get; set; }
    public string Unit { get; set; }
    public string BatchNo { get; set; }
    public string PalletNo { get; set; }
}

/// <summary>
/// 托盘解绑请求模型
/// </summary>
public class PalletReleaseRequest
{
    /// <summary>
    /// 托盘号
    /// </summary>
    public string PalletNo { get; set; }
}

/// <summary>
/// 托盘解绑响应模型
/// </summary>
public class PalletReleaseResponse
{
    /// <summary>
    /// 状态标志 true:成功，false：失败
    /// </summary>
    public bool success { get; set; }

    /// <summary>
    /// 错误描述
    /// </summary>
    public string msg { get; set; }
}
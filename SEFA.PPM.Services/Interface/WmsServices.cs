using System;
using System.Threading.Tasks;
using MongoDB.Bson;
using SEFA.Base.Common;
using SEFA.Base.Common.HttpRestSharp;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.Model;
using SEFA.Base.Services.BASE;
using SEFA.PPM.IServices;
using SEFA.PPM.IServices.Models;

namespace SEFA.PPM.Services.Interface;

public class WmsServices : IWmsServices
{
    private static readonly string _callMaterialUrl = Appsettings.app("WMS_Interface_Url", "CallMaterialUrl");
    private static readonly string _sendDistributionMaterialUrl = Appsettings.app("WMS_Interface_Url", "SendDistributionMaterialUrl");
    
    #region WMS叫料
    /// <summary>
    /// 异步调用叫料接口
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    public async Task<MessageModel<String>> CallMaterialAsync(CallMaterialRequest request)
    {
        MessageModel<String> result = new MessageModel<String>();
        result.success = true;
        result.msg = "成功";
        result.response = "成功";
        try
        {
            // 调用HTTP辅助类发送POST请求到指定接口
            var apiResult = await HttpHelper.PostAsync<string>("WMS", _callMaterialUrl, null, request);
            // 记录返回内容的调试日志
            SerilogServer.LogDebug($"返回内容：" + apiResult.ToJson(), "CallMaterial");
            // 检查返回结果是否为空
            if (apiResult == null)
            {
                // 如果返回为空，设置结果为失败并记录错误消息
                result.success = false;
                result.msg = "叫料接口返回为空！";
                SerilogServer.LogDebug(result.msg, "CallMaterial");
                return result;
            }

            // 将接口返回的结果赋值给本地结果对象
            result.success = apiResult.success;
            result.msg = apiResult.msg;
            // 记录接口返回消息的调试日志
            SerilogServer.LogDebug(result.msg, "CallMaterial");
        }
        catch (Exception ex)
        {
            // 捕获异常，设置结果为失败并记录异常堆栈信息
            result.success = false;
            result.msg = "叫料接口调用异常：" + ex.Message;
            SerilogServer.LogDebug(result.msg, "CallMaterial");
            return result;
        }

        return result;
    }

    #endregion

    #region WMS物料到达通知
    /// <summary>
    /// 异步调用物料到达通知接口
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    public async Task<MessageModel<bool>> SendDistributionMaterialAsync(DistributionMaterialRequest request)
    {
        MessageModel<bool> result = new MessageModel<bool>();
        result.success = true;
        result.msg = "成功";
        result.response = true;
        try
        {
            // 调用HTTP辅助类发送POST请求到指定接口
            var apiResult = await HttpHelper.PostAsync<bool>("WMS", _sendDistributionMaterialUrl, null, request);
            // 记录返回内容的调试日志
            SerilogServer.LogDebug($"返回内容：" + apiResult.ToJson(), "SendDistributionMaterial");
            // 检查返回结果是否为空
            if (apiResult == null)
            {
                // 如果返回为空，设置结果为失败并记录错误消息
                result.success = false;
                result.msg = "物料到达通知接口返回为空！";
                SerilogServer.LogDebug(result.msg, "SendDistributionMaterial");
                return result;
            }

            // 将接口返回的结果赋值给本地结果对象
            result.success = apiResult.success;
            result.msg = apiResult.msg;
            // 记录接口返回消息的调试日志
            SerilogServer.LogDebug(result.msg, "SendDistributionMaterial");
        }
        catch (Exception ex)
        {
            // 捕获异常，设置结果为失败并记录异常堆栈信息
            result.success = false;
            result.msg = "物料到达通知接口调用异常：" + ex.Message;
            SerilogServer.LogDebug(result.msg, "SendDistributionMaterial");
            return result;
        }

        return result;
    }
    #endregion

    
    
    
}